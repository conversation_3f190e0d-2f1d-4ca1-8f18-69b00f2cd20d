import React, { useRef, useEffect } from 'react';
import { View, Animated, LayoutAnimation, Platform, UIManager } from 'react-native';
import { Card } from 'react-native-paper';
import createRecipeStyles from '@/styles/RecipeStyles';
import { useColorScheme } from 'react-native';
import { Recipe, InstructionType, Ingredient } from '@/components/types';
import RecipeCard from './RecipeCard';
import ExpandedRecipeCard from './ExpandedRecipeCard';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface AnimatedRecipeCardProps {
  recipe: Recipe;
  isExpanded: boolean;
  isFavorite: boolean;
  servings: number;
  instructionType: InstructionType;
  instructionTypes: InstructionType[];
  isLoadingDetails: boolean;
  isGeneratingDetails: boolean;
  lastResponseId: string | null;
  onToggleFavorite: (recipeId: string, e: any, recipe?: Recipe, lastResponseId?: string | null) => void;
  onToggleExpanded: (recipeId: string) => void;
  onSelectInstructionType: (type: InstructionType) => void;
  onChangeServings: (servings: number) => void;
}

const AnimatedRecipeCard: React.FC<AnimatedRecipeCardProps> = ({
  recipe,
  isExpanded,
  isFavorite,
  servings,
  instructionType,
  instructionTypes,
  isLoadingDetails,
  isGeneratingDetails,
  lastResponseId,
  onToggleFavorite,
  onToggleExpanded,
  onSelectInstructionType,
  onChangeServings,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const recipeStyles = createRecipeStyles(colorScheme as 'light' | 'dark');
  
  const scaleAnim = useRef(new Animated.Value(1)).current;

  // Configure fast and minimal layout animation
  const configureLayoutAnimation = () => {
    LayoutAnimation.configureNext({
      duration: 200,
      create: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      update: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
      delete: {
        type: LayoutAnimation.Types.easeInEaseOut,
        property: LayoutAnimation.Properties.opacity,
      },
    });
  };

  // Handle expansion animation
  useEffect(() => {
    configureLayoutAnimation();
  }, [isExpanded]);

  const handlePress = () => {
    // Add a fast, minimal press animation
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.98,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start();

    // Trigger expansion immediately
    onToggleExpanded(recipe.id);
  };

  const animatedStyle = {
    transform: [{ scale: scaleAnim }],
  };

  return (
    <View style={recipeStyles.recipeCardContainer}>
      <Animated.View style={animatedStyle}>
        {isExpanded ? (
          <ExpandedRecipeCard
            title={recipe.title}
            timeInMinutes={recipe.timeInMinutes}
            calories={recipe.calories}
            imageUrl={recipe.imageUrl}
            instructions={recipe.instructions}
            ingredients={recipe.ingredients}
            isFavorite={isFavorite}
            servings={servings}
            instructionType={instructionType}
            instructionTypes={instructionTypes}
            isLoading={isLoadingDetails || isGeneratingDetails}
            recipe={recipe}
            onToggleFavorite={(e: any) => onToggleFavorite(recipe.id, e, recipe, lastResponseId)}
            onSelectInstructionType={onSelectInstructionType}
            onChangeServings={onChangeServings}
            onPress={handlePress}
          />
        ) : (
          <RecipeCard
            title={recipe.title}
            timeInMinutes={recipe.timeInMinutes}
            calories={recipe.calories}
            imageUrl={recipe.imageUrl}
            isFavorite={isFavorite}
            recipe={recipe}
            onToggleFavorite={(e: any) => onToggleFavorite(recipe.id, e, recipe, lastResponseId)}
            onPress={handlePress}
          />
        )}
      </Animated.View>
    </View>
  );
};

export default AnimatedRecipeCard;

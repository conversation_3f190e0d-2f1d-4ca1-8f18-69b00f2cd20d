import React, { useState, useRef } from 'react';
import { View, Image, Animated, StyleSheet, ImageStyle, ViewStyle } from 'react-native';
import { ActivityIndicator } from 'react-native-paper';
import { useColorScheme } from 'react-native';
import { getThemeColors } from '@/styles/Theme';

interface SmoothImageProps {
  source: { uri: string };
  style?: ImageStyle;
  containerStyle?: ViewStyle;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  showLoader?: boolean;
  loaderSize?: 'small' | 'large';
  fadeDuration?: number;
  placeholder?: React.ReactNode;
}

const SmoothImage: React.FC<SmoothImageProps> = ({
  source,
  style,
  containerStyle,
  resizeMode = 'cover',
  showLoader = true,
  loaderSize = 'large',
  fadeDuration = 300,
  placeholder,
}) => {
  const colorScheme = useColorScheme() || 'light';
  const colors = getThemeColors(colorScheme as 'light' | 'dark');
  
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;

  const handleLoadStart = () => {
    setIsLoading(true);
    setHasError(false);
    fadeAnim.setValue(0);
  };

  const handleLoadEnd = () => {
    setIsLoading(false);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: fadeDuration,
      useNativeDriver: true,
    }).start();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  const renderLoader = () => {
    if (!showLoader || !isLoading) return null;

    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size={loaderSize} color={colors.accent} />
      </View>
    );
  };

  const renderPlaceholder = () => {
    if (!hasError && !placeholder) return null;

    return (
      <View style={styles.placeholderContainer}>
        {hasError ? (
          <View style={styles.errorContainer}>
            <View style={[styles.errorPlaceholder, { backgroundColor: colors.surface }]} />
          </View>
        ) : (
          placeholder
        )}
      </View>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Background loader or placeholder */}
      {renderLoader()}
      {renderPlaceholder()}
      
      {/* Main image with fade animation */}
      {!hasError && (
        <Animated.View style={[styles.imageContainer, { opacity: fadeAnim }]}>
          <Image
            source={source}
            style={[styles.image, style]}
            resizeMode={resizeMode}
            onLoadStart={handleLoadStart}
            onLoadEnd={handleLoadEnd}
            onError={handleError}
          />
        </Animated.View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  imageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  placeholderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorPlaceholder: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    opacity: 0.3,
  },
});

export default SmoothImage;
